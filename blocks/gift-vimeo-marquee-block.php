<?php
    $videos = get_field('videos'); // Array met rows, ieder row heeft ['url']
?>

<section class="mediaMarqueeBlock <?php the_field("background") ?>" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="marqueeWrapper" data-init>
        <div class="marquee" data-marquee data-marquee-direction="right" data-marquee-speed="25" data-marquee-scroll-speed="5" data-marquee-swipe="true">
            <?php if( $videos ): ?>
                <?php if( count($videos) > 1 ): ?>
                    <div class="marqueeScroll">

                        <?php for($i=0; $i<4; $i++): ?>
                            <div class="itemsContainer">
                                <?php foreach( $videos as $video ): ?>
                                    <?php
                                        $video_url = $video['url'] ?? '';
                                        preg_match('/vimeo\.com\/(\d+)/', $video_url, $matches);
                                        $video_id = $matches[1] ?? null;

                                        $thumb = '';
                                        if ($video_id) {
                                            $response = wp_remote_get("https://vimeo.com/api/v2/video/$video_id.json");
                                            if (!is_wp_error($response)) {
                                                $data = json_decode(wp_remote_retrieve_body($response));
                                                if (!empty($data[0]->thumbnail_large)) {
                                                    $thumb = esc_url($data[0]->thumbnail_large);
                                                }
                                            }
                                        }
                                    ?>
                                    <?php if ($video_id): ?>
                                        <div class="item">
                                            <video 
                                                class="lazy" 
                                                playsinline 
                                                autoplay 
                                                muted 
                                                loop 
                                                preload="none"
                                                poster="<?php echo $thumb; ?>">
                                                <source src="https://player.vimeo.com/progressive_redirect/playback/<?php echo $video_id; ?>/rendition/720p/file.mp4?loc=external&signature=<?php echo md5($video_id); ?>" type="video/mp4">
                                            </video>
                                            <div class="play-icon">
                                                <svg width="68" height="48" viewBox="0 0 68 48">
                                                    <path d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.2
                                                    7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,
                                                    34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.
                                                    74z" fill="#f00"></path>
                                                    <path d="M 45,24 27,14 27,34" fill="#fff"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        <?php endfor; ?>

                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</section>
