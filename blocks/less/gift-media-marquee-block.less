// out: false
.mediaMarqueeBlock {
    .marquee {
        white-space: nowrap;
        .itemsContainer {
            display: inline-block;
            .item {
                display: inline-block;
                height: @vw100 * 4;
                position: relative;
                overflow: hidden;
                .rounded(@vw20);
                width: (@vw112 * 2) + (@vw16 * 2);
                margin: 0 @vw8;
                &:nth-child(even) {
                    width: (@vw112 * 3) + (@vw16 * 3);
                }
                img {
                    position: absolute;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    pointer-events: none;
                    object-fit: cover;
                }
            }
        }
    }
}

@media (max-width: 1080px) {
    .mediaMarqueeBlock {
      .marquee {
        .itemsContainer {
          .item {
            height: @vw100-1080 * 3.5; // <PERSON>ere hoogte voor betere schaalbaarheid
            width: (@vw112-1080 * 1.8) + (@vw16-1080 * 2);
            margin: 0 @vw6-1080;
  
            &:nth-child(even) {
              width: (@vw112-1080 * 2.5) + (@vw16-1080 * 3);
            }
          }
        }
      }
    }
  }
  
  @media (max-width: 580px) {
    .mediaMarqueeBlock {
      .marquee {
        .itemsContainer {
          .item {
            height: @vw100-580 * 3.5; // Kleinere hoogte voor betere schaalbaarheid
            width: (@vw112-580 * 1.8) + (@vw16-580 * 2);
            margin: 0 @vw6-580;
  
            &:nth-child(even) {
              width: (@vw112-580 * 2.5) + (@vw16-580 * 3);
            }
          }
        }
      }
    }
  }
  